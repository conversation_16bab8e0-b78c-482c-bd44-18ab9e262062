// To parse this JSON data, do
//
//     final upStreamModel = upStreamModelFromJson(jsonString);

import 'dart:convert';

List<UpStreamModel> upStreamModelFromJson(String str) =>
    List<UpStreamModel>.from(
      json.decode(str).map((x) => UpStreamModel.fromJson(x)),
    );

String upStreamModelToJson(List<UpStreamModel> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class UpStreamModel {
  String userId;
  String name;
  String email;
  String role;
  int recruitsCount;
  int level;

  UpStreamModel({
    required this.userId,
    required this.name,
    required this.email,
    required this.role,
    required this.recruitsCount,
    required this.level,
  });

  factory UpStreamModel.fromJson(Map<String, dynamic> json) => UpStreamModel(
    userId: json["userId"],
    name: json["name"],
    email: json["email"],
    role: json["role"],
    recruitsCount: json["recruitsCount"],
    level: json["level"],
  );

  Map<String, dynamic> toJson() => {
    "userId": userId,
    "name": name,
    "email": email,
    "role": role,
    "recruitsCount": recruitsCount,
    "level": level,
  };
}
