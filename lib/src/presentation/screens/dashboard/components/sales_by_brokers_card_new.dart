import 'dart:math' show min;
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/config/app_strings.dart';
import '../../../../core/config/constants.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../core/theme/app_fonts.dart';
import '../../../../core/config/responsive.dart';
import '../../../../domain/models/top_performers.dart';

class SalesByBrokersCard extends HookWidget {
  final List<TopPerformers> brokersList;
  final List<TopPerformers> agentsList;
  final bool isBrokerView;
  static const List<Color> colorPalette = [
    Color(0xFF2196F3),
    Color(0xFFFF9800),
    Color(0xFF4CAF50),
    Color(0xFF9C27B0),
    Color(0xFFF44336),
    Color(0xFF009688),
    Color(0xFF3F51B5),
    Color(0xFF795548),
    Color(0xFF00BCD4),
    Color(0xFFFF5722),
    Color(0xFFE91E63),
    Color(0xFF607D8B),
    Color(0xFF8BC34A),
    Color(0xFFFFEB3B),
    Color(0xFF673AB7),
    Color(0xFF00E676),
    Color(0xFFFF6F00),
    Color(0xFF1DE9B6),
    Color(0xFF536DFE),
    Color(0xFFFF4081),
  ];

  const SalesByBrokersCard({
    super.key,
    required this.isBrokerView,
    required this.brokersList,
    required this.agentsList,
  });

  @override
  Widget build(BuildContext context) {
    final touchedIndex = useState<int>(-1);
    final chartSectionsCount = useState<int>(0);

    useEffect(() {
      chartSectionsCount.value = isBrokerView
          ? brokersList.length
          : agentsList.length;
      return null;
    }, [isBrokerView, brokersList, agentsList]);

    final Size size = MediaQuery.of(context).size;
    final bool isTablet = Responsive.isTablet(context);

    return Container(
      margin: isTablet
          ? EdgeInsets.fromLTRB(
              0,
              defaultPadding / 1.5,
              defaultPadding / 1.5,
              defaultPadding / 1.5,
            )
          : const EdgeInsets.symmetric(horizontal: defaultPadding / 1.5),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: Text(
                salesTab,
                textAlign: TextAlign.center,
                style: AppFonts.semiBoldTextStyle(
                  16,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            Expanded(
              child: Responsive(
                smallMobile: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
                mobile: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    _pieChart(context, size, touchedIndex, chartSectionsCount),
                    _pieChartLegend(context, touchedIndex, chartSectionsCount),
                  ],
                ),
                tablet: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
                desktop: _pieChartColumnView(
                  context,
                  size,
                  touchedIndex,
                  chartSectionsCount,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pieChartColumnView(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        _pieChart(context, size, touchedIndex, chartSectionsCount),
        _pieChartLegend(context, touchedIndex, chartSectionsCount),
      ],
    );
  }

  Widget _pieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return Expanded(
      flex: 2,
      child: LayoutBuilder(
        builder: (context, constraints) {
          final minDimension = min(constraints.maxWidth, constraints.maxHeight);
          final chartSize = minDimension * 0.8; // Use 80% of available space
          
          return Center(
            child: SizedBox(
              width: chartSize,
              height: chartSize,
              child: Stack(
                alignment: Alignment.center,
                children: [
                  _buildPieChart(context, size, touchedIndex, chartSectionsCount),
                  if (touchedIndex.value != -1) _chartBadge(touchedIndex),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildPieChart(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    return PieChart(
      PieChartData(
        pieTouchData: PieTouchData(
          touchCallback: (FlTouchEvent event, pieTouchResponse) {
            if (pieTouchResponse != null &&
                pieTouchResponse.touchedSection != null &&
                pieTouchResponse.touchedSection!.touchedSectionIndex !=
                    touchedIndex.value) {
              if (!event.isInterestedForInteractions) {
                touchedIndex.value = -1;
                return;
              }
              touchedIndex.value =
                  pieTouchResponse.touchedSection!.touchedSectionIndex;
            }
          },
        ),
        borderData: FlBorderData(show: false),
        sectionsSpace: 1.5,
        centerSpaceRadius: 0,
        sections: _showingSections(
          context,
          size,
          touchedIndex,
          chartSectionsCount,
        ),
      ),
    );
  }

  List<PieChartSectionData> _showingSections(
    BuildContext context,
    Size size,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    final isTablet = Responsive.isTablet(context);
    return List.generate(chartSectionsCount.value, (i) {
      final isTouched = i == touchedIndex.value;
      final radius = isTouched
          ? isTablet
              ? 110.0
              : 70.0
          : isTablet
              ? 100.0
              : 65.0;

      return PieChartSectionData(
        showTitle: false,
        borderSide: BorderSide.none,
        color: colorPalette[i % colorPalette.length],
        value: isBrokerView
            ? brokersList[i].monthlyRevenue.toDouble()
            : agentsList[i].monthlyRevenue.toDouble(),
        title: '',
        radius: radius,
        titleStyle: AppFonts.semiBoldTextStyle(0, color: Colors.white),
      );
    });
  }

  Widget _chartBadge(ValueNotifier<int> touchedIndex) {
    final color = colorPalette[touchedIndex.value % colorPalette.length];
    final name = isBrokerView
        ? brokersList[touchedIndex.value].name
        : agentsList[touchedIndex.value].name;
    final sales = isBrokerView
        ? brokersList[touchedIndex.value].monthlySalesCount
        : agentsList[touchedIndex.value].monthlySalesCount;

    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color,
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              name,
              style: AppFonts.semiBoldTextStyle(11, color: Colors.white),
            ),
            Text(
              '$sales $salesTab',
              style: AppFonts.semiBoldTextStyle(9, color: Colors.white),
            ),
          ],
        ),
      ),
    );
  }

  Widget _pieChartLegend(
    BuildContext context,
    ValueNotifier<int> touchedIndex,
    ValueNotifier<int> chartSectionsCount,
  ) {
    final size = MediaQuery.sizeOf(context);
    final isDesktop = Responsive.isDesktop(context);
    final isTablet = Responsive.isTablet(context);

    return Expanded(
      flex: 1,
      child: Container(
        margin: const EdgeInsets.fromLTRB(
          defaultMargin,
          defaultMargin / 2,
          defaultMargin,
          defaultMargin,
        ),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.salesLegendBorderColor, width: 1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: ListView.builder(
            shrinkWrap: true,
            padding: const EdgeInsets.all(8),
            itemCount: chartSectionsCount.value,
            itemBuilder: (context, index) {
              return Container(
                height: 48,
                margin: const EdgeInsets.only(bottom: 4),
                decoration: BoxDecoration(
                  color: touchedIndex.value == index
                      ? colorPalette[index % colorPalette.length].withOpacity(0.15)
                      : Colors.transparent,
                  border: Border(
                    bottom: BorderSide(
                      color: AppTheme.salesLegendBorderColor,
                      width: 1,
                    ),
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => touchedIndex.value = index,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 2,
                      ),
                      child: Row(
                        children: [
                          Container(
                            width: 10,
                            height: 10,
                            decoration: BoxDecoration(
                              color: colorPalette[index % colorPalette.length],
                              borderRadius: BorderRadius.circular(3),
                            ),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _legendUserInfo(
                              index,
                              isDesktop || isTablet,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _legendUserInfo(int index, bool isLargeScreen) {
    final data = isBrokerView ? brokersList[index] : agentsList[index];
    final textColor = AppTheme.primaryTextColor;
    final textSize = isLargeScreen ? 14.0 : 12.0;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          data.name,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
          style: AppFonts.semiBoldTextStyle(textSize, color: textColor),
        ),
        const SizedBox(height: 2),
        Text(
          '\$${data.monthlyRevenue}',
          style: AppFonts.regularTextStyle(textSize - 2, color: textColor),
        ),
      ],
    );
  }
}
